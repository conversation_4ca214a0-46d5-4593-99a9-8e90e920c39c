// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

// Flutter imports:
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../core/network/ez_network.dart';
import '../../../core/params/request_params.dart';
import '../../../core/utils/mappers.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../data/models/models.dart';
import '../../../domain/entities/entities.dart';
import '../../../injector/injector.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../widgets/image_stack.dart';
import '../../widgets/widgets.dart';
import '../bloc/add_tags_image_bloc.dart';
import 'search_tag_view.dart';

class CreateTagScreen extends StatefulWidget {
  const CreateTagScreen({
    required this.selectedImageList,
    this.imageBytes,
    required this.tagList,
    required this.onBack,
    required this.selectedCustomer,
    super.key,
  });

  final List<AddTagsImageGetImageListItemsImages?> selectedImageList;
  final Uint8List? imageBytes;
  final Function()? onBack;
  final List<AddTagsImageGetTagListItems?> tagList;
  final CustomerListItem? selectedCustomer;

  @override
  State<CreateTagScreen> createState() => _CreateTagScreenState();
}

class _CreateTagScreenState extends State<CreateTagScreen> {
  final TextEditingController searchController = TextEditingController();
  final List<AddTagsImageGetTagListItems?> selectedTagList = [];
  final List<AddTagsImageGetTagListItems?> popularTags = [];
  final List<AddTagsImageGetTagListItems?> searchTags = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      final dynamicList = await EZCache.shared.get('tagHistory');
      final tagHistoryId = (dynamicList as List<dynamic>)
          .map((final e) => e as String?)
          .toList();
      popularTags.addAll(
        tagHistoryId
            .map(
              (final id) => widget.tagList.firstWhere(
                (final element) => element?.tagID == id,
              ),
            )
            .toList(),
      );
      setState(() {});
    });

    searchTags.addAll(widget.tagList);
  }

  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      leading: IconButton(
        onPressed: widget.onBack,
        icon: EZResources.image(
          ImageParams(
            name: AppIcons.icBack,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      title: Text(
        context.l10n.addTags,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: 18),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoTile(
                      context,
                      title: widget.selectedCustomer?.customerName ?? '',
                      subtitle: widget.selectedCustomer?.customerCode ?? '',
                    ),
                    const SizedBox(height: 12),
                    _buildImageTile(context),
                    const SizedBox(height: 20),
                    Text(
                      'TAGS',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => onTapSearch(context),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            width: 0.35,
                            color: const Color(0xffBCB9C0),
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                context.l10n.selectTags,
                                style: Theme.of(context).textTheme.bodyLarge
                                    ?.copyWith(
                                      color: Theme.of(context).hintColor,
                                    ),
                              ),
                            ),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: Theme.of(context).hintColor,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: selectedTagList.map((final tag) {
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (selectedTagList.contains(tag)) {
                                selectedTagList.remove(tag);
                              }
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.only(
                              top: 8,
                              left: 12,
                              right: 8,
                              bottom: 8,
                            ),
                            decoration: ShapeDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Text(
                                  tag?.tagName ?? '',
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.surface,
                                      ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.surface,
                                  ),
                                  padding: const EdgeInsets.all(2),
                                  child: Icon(
                                    Icons.close,
                                    color: Theme.of(context).primaryColor,
                                    size: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.only(left: 12, bottom: 10),
                      child: Text(
                        context.l10n.popularTags,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).hintColor,
                        ),
                      ),
                    ),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: popularTags.map((final tag) {
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (!selectedTagList.contains(tag)) {
                                selectedTagList.add(tag);
                              }
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.only(
                              top: 8,
                              left: 12,
                              right: 8,
                              bottom: 8,
                            ),
                            decoration: ShapeDecoration(
                              color: Colors.white /* color-white-white */,
                              shape: RoundedRectangleBorder(
                                side: const BorderSide(
                                  color: Color(0xFFC7C7C7),
                                ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Text(
                                  tag?.tagName ?? '',
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(
                                        color: Theme.of(context).hintColor,
                                      ),
                                ),
                                Icon(
                                  Icons.add_circle_outline,
                                  color: Theme.of(context).hintColor,
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  final user = EZCache.shared.getUserProfile();
                  final selectedTagListParams = selectedTagList
                      .map(
                        (final tag) =>
                            getIt<Mapper>().tryConvert<
                              AddTagsImageGetTagListItems,
                              AddTagsImageGetTagListItemsModel
                            >(tag) ??
                            AddTagsImageGetTagListItemsModel(),
                      )
                      .toList();
                  if (widget.selectedImageList.isNotEmpty) {
                    context.read<AddTagsImageBloc>().add(
                      AddTagsImageCreateImageTaged(
                        AddTagsImageCreateImageTagRequestParams(
                          partnerId:
                              widget.selectedCustomer?.customerCode ?? '',
                          partnerName:
                              widget.selectedCustomer?.customerName ?? '',
                          ids: widget.selectedImageList
                              .map((final img) => img?.id ?? '')
                              .toList(),
                          tags: selectedTagListParams,
                          createdBy: user?.employeeId ?? '',
                          createdName: user?.name ?? '',
                        ),
                      ),
                    );
                    return;
                  }
                  if (widget.imageBytes != null) {
                    context.read<AddTagsImageBloc>().add(
                      AddTagsImageCreateMergeImageed(
                        AddTagsImageCreateMergeImageRequestParams(
                          partnerId:
                              widget.selectedCustomer?.customerCode ?? '',
                          partnerName:
                              widget.selectedCustomer?.customerName ?? '',
                          tags: jsonEncode(selectedTagListParams),
                          createdBy: user?.employeeId ?? '',
                          createdName: user?.name ?? '',
                          imageMerge: MultipartFile.fromBytes(
                            widget.imageBytes!,
                            filename:
                                '${user?.employeeId}-'
                                '${DateTime.now().millisecondsSinceEpoch}.png',
                          ),
                          mergeIds: widget.selectedImageList
                              .map((final img) => img?.id ?? '')
                              .toList()
                              .join(','),
                        ),
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  context.l10n.complete,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onTapSearch(final BuildContext context) async {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,

      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(14)),
      ),
      builder: (final _) => ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
        child: SizedBox(
          height: MediaQuery.sizeOf(context).height * 0.9,
          child: StatefulBuilder(
            builder: (final _, final modalState) {
              return SearchTagView(
                searchController: searchController,
                selectedTagList: selectedTagList,
                onChanged: (final keySearch) {
                  searchTags.clear();
                  searchTags.addAll(
                    widget.tagList
                        .where(
                          (final tag) =>
                              tag?.tagName?.toLowerCase().contains(
                                keySearch.toLowerCase(),
                              ) ??
                              false,
                        )
                        .toList(),
                  );
                  modalState(() {});
                },
                onClear: () {
                  searchController.clear();
                  searchTags.clear();
                  searchTags.addAll(widget.tagList);
                  modalState(() {});
                },
                onSelected: (final tag) {
                  if (selectedTagList.length >= 5) {
                    final list = selectedTagList
                        .sublist(0, 5)
                        .map((final tag) => tag?.tagID ?? '')
                        .toList();
                    EZCache.shared.save('tagHistory', list.toSet().toList());
                  } else {
                    final popList = popularTags
                        .map((final tag) => tag?.tagID ?? '')
                        .toList();

                    final list = popList
                      ..insertAll(
                        0,
                        selectedTagList
                            .map((final tag) => tag?.tagID ?? '')
                            .toList(),
                      );

                    EZCache.shared.save(
                      'tagHistory',
                      list.length > 5
                          ? list.sublist(0, 5).toSet().toList()
                          : list.toSet().toList(),
                    );
                  }
                  modalState(() {});
                },
                tagList: searchTags,
              );
            },
          ),
        ),
      ),
    ).whenComplete(() {
      searchController.clear();
      searchTags.clear();
      searchTags.addAll(widget.tagList);
    });
  }

  Widget _buildInfoTile(
    final BuildContext context, {
    required final String title,
    required final String subtitle,
  }) {
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontSize: 15),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 13,
              color: Theme.of(context).hintColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageTile(final BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (widget.imageBytes != null) {
          showGeneralDialog(
            context: context,
            transitionBuilder:
                (final _, final animation, final __, final child) {
                  return FadeTransition(
                    opacity: animation,
                    child: ScaleTransition(
                      scale: CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutBack,
                      ),
                      child: child,
                    ),
                  );
                },
            pageBuilder: (_, final __, final ___) {
              return Scaffold(
                appBar: AppBar(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                ),
                backgroundColor: Colors.black,
                body: Center(
                  child: InteractiveViewer(
                    child: Image.memory(widget.imageBytes!),
                  ),
                ),
              );
            },
          );
          return;
        }
        if (widget.selectedImageList.isNotEmpty) {
          showGeneralDialog(
            context: context,
            transitionBuilder:
                (final _, final animation, final __, final child) {
                  return FadeTransition(
                    opacity: animation,
                    child: ScaleTransition(
                      scale: CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutBack,
                      ),
                      child: child,
                    ),
                  );
                },
            pageBuilder: (_, final __, final ___) {
              return ImageFullView(
                isShowCount: true,
                imageUrls: widget.selectedImageList
                    .map((final url) => ImageProperty(url: url?.imageUrl))
                    .toList(),
              );
            },
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(2),
              child: widget.selectedImageList.isEmpty
                  ? Image.memory(
                      widget.imageBytes ?? Uint8List(0),
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                    )
                  : ImageStackWidget(
                      imageUrls: widget.selectedImageList
                          .map((final url) => url?.imageUrl ?? '')
                          .toList(),
                    ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${widget.selectedImageList.length} '
                  '${context.l10n.imageIsSelected.toLowerCase()}',
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontSize: 15),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.imageBytes != null
                      ? context.l10n.mergedImage
                      : context.l10n.singleImage,
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const Spacer(),
            Icon(
              Icons.chevron_right,
              color: Theme.of(context).hintColor,
              size: 32,
            ),
          ],
        ),
      ),
    );
  }
}
