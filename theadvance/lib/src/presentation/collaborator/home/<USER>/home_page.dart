// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../core/nd_constants/strings.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/routes/routes.dart';
import '../../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../../data/datasources/local/key/keys.dart';
import '../../../../data/models/api_models.dart';
import '../../../../injector/injector.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../widgets/widgets.dart';
import '../bloc/home_bloc.dart';
import '../widgets/widgets.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({final Key? key}) : super(key: key);
  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  final ValueNotifierList<ServiceItemsModel?> favoriteCodeList =
      ValueNotifierList([]);
  final ValueNotifierList<ServiceItemsModel?> mainCodeList = ValueNotifierList(
    [],
  );
  UserModel? user;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      await _loadFavoriteServices();
      await _loadUserProfile();
    });
  }

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<HomeBloc>()..add(HomeFetched()),
      child: BaseLayout(
        leading: Padding(
          padding: const EdgeInsets.only(left: 12),
          child: GestureDetector(
            onTap: () {
              context.router.push(
                StoryPersonRoute(codeUser: user?.employeeId ?? Strings.empty),
              );
            },
            child: _buildUserProfile(user),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () async {
              context.router.pushNamed(Routes.notifications);
            },
            icon: EZResources.image(
              ImageParams(
                name: AppIcons.icNotiOutline,
                size: const ImageSize(22, 22),
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          // IconButton(
          //   onPressed: () async {
          //     context.router.pushNamed(Routes.editHomeMenu).then((
          //       final _,
          //     ) async {
          //       await _loadFavoriteServices();
          //     });
          //   },
          //   icon: EZResources.image(ImageParams(name: AppIcons.icCustom)),
          // ),
        ],
        title: const SizedBox.shrink(),
        body: HomeBody(
          favoriteCodeList: favoriteCodeList,
          mainCodeList: mainCodeList,
        ),
      ),
    );
  }

  Widget _buildUserProfile(final UserModel? user) {
    final names = user?.name?.split(' ');
    return Row(
      children: [
        ClipOval(
          child: EzCachedNetworkImage(
            imageUrl: user?.avatar,
            fit: BoxFit.contain,
            width: _itemWidth(context),
            height: _itemWidth(context),
          ),
        ),
        const SizedBox(width: 12),
        Column(
          spacing: 4,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              TextSpan(
                text: 'Xin chào ',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
                children: [
                  TextSpan(
                    text: names?.sublist(names.length - 2).join(' '),
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  double _itemWidth(final BuildContext context) {
    return MediaQuery.sizeOf(context).width / 8.9;
  }

  Future<void> _loadFavoriteServices() async {
    try {
      final cachePin = await EZCache.shared.getHomeItem(
        CollaboratorKeys.pinServices,
      );
      final cacheMain = await EZCache.shared.getHomeItem(
        CollaboratorKeys.mainServices,
      );

      if (cachePin.isNotEmpty) {
        favoriteCodeList.setValue(cachePin.map((final e) => e).toList());
      } else {
        favoriteCodeList.setValue([
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'SEARCH_CUSTOMER',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'WORKING',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'ADVISORY',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'CREATE_CUSTOMER',
              ) ??
              ServiceItemsModel(),
        ]);
        EZCache.shared.saveHomeItem(
          CollaboratorKeys.pinServices,
          favoriteCodeList.value,
        );
      }
      if (cacheMain.isNotEmpty) {
        mainCodeList.setValue(cacheMain.map((final e) => e).toList());
      } else {
        mainCodeList.setValue([
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'CHECK_IN',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'BOOKING_MEAL',
              ) ??
              ServiceItemsModel(),
        ]);
        EZCache.shared.saveHomeItem(
          CollaboratorKeys.mainServices,
          mainCodeList.value,
        );
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      user = EZCache.shared.getUserProfile();
    } catch (_) {}
  }

  // Method to force reload favorite services (useful for debugging)
  void reloadFavoriteServices() {
    _loadFavoriteServices();
  }
}
