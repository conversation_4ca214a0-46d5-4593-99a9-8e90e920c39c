// Dart imports:
import 'dart:async';
import 'dart:typed_data';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

// Project imports:
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/params/request_params.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/entities.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../widgets/widgets.dart';
import '../bloc/add_tags_image_bloc.dart';
import 'complete_view.dart';
import 'search_customer_view.dart';
import 'toggle_button.dart';

// Package imports:

class AddTagsImageBody extends StatefulWidget {
  const AddTagsImageBody({final Key? key}) : super(key: key);

  @override
  State<AddTagsImageBody> createState() => _AddTagsImageBodyState();
}

class _AddTagsImageBodyState extends State<AddTagsImageBody> {
  AddTagsImage? data;
  CustomerListItem? selectedCustomer;

  final List<CustomerGetRoomListItem?> roomList = [];
  final List<CustomerListItem?> customerList = [];
  final List<AddTagsImageGetImageListItems?> imageList = [];
  final List<String?> selectedDates = [];
  final List<String> selectedRoomIds = [];
  final List<AddTagsImageGetTagListItems?> tagList = [];
  bool isSingle = true;
  bool isHorizontalMerge = true;
  bool showCompleteView = false;
  Uint8List? mergedImage;

  final List<AddTagsImageGetImageListItemsImages?> selectedImageList = [];
  final TextEditingController searchController = TextEditingController();
  WidgetsToImageController horizontalController = WidgetsToImageController();
  WidgetsToImageController verticalController = WidgetsToImageController();

  Timer? timer;
  @override
  void initState() {
    super.initState();
    selectedRoomIds.add('');
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      onTapSearch();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<AddTagsImageBloc, AddTagsImageState>(
      listener: (final context, final state) {
        if (state.status == AddTagsImageStatus.success) {
          data = Utils.getData(state.data);
        }
        if (state.status == AddTagsImageStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
        if (state.status == AddTagsImageStatus.userSearchSuccess) {
          customerList.clear();

          customerList.addAll(
            Utils.getData<CustomerList>(state.data)?.items ?? [],
          );
        }
        if (state.status == AddTagsImageStatus.getRoomListSuccess) {
          roomList.clear();
          roomList.add(CustomerGetRoomListItem(roomName: context.l10n.all));
          roomList.addAll(
            Utils.getData<CustomerGetRoomList>(state.data)?.items ?? [],
          );
        }
        if (state.status == AddTagsImageStatus.getImageListSuccess) {
          imageList.clear();
          imageList.addAll(state.data);
        }

        if (state.status == AddTagsImageStatus.userSearchSuccess) {
          customerList.clear();
          customerList.addAll(
            Utils.getData<CustomerList>(state.data)?.items ?? [],
          );
        }
        if (state.status == AddTagsImageStatus.getTagListSuccess) {
          tagList.clear();
          tagList.addAll(state.data);
        }
        if (state.status == AddTagsImageStatus.createImageTagSuccess ||
            state.status == AddTagsImageStatus.createMergeImageSuccess) {
          if (showCompleteView) {
            setState(() {
              showCompleteView = false;
            });
            selectedDates.clear();
            selectedRoomIds.clear();
            selectedImageList.clear();
          }
          context.read<AddTagsImageBloc>().add(
            AddTagsImageGetImageListed(
              AddTagsImageGetImageListRequestParams(
                selectedCustomer?.customerCode,
                itemGroupId: selectedRoomIds.firstOrNull,
              ),
            ),
          );
          EzToast.showWidgetToast(
            context,
            duration: const Duration(seconds: 3),
            body: DecoratedBox(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 12.0,
                  ),
                  child: Row(
                    spacing: 8,
                    children: [
                      DecoratedBox(
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(2.0),
                          child: Icon(
                            Icons.check,
                            size: 24,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      Text(
                        context.l10n.addTagImageSuccess,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
      },
      builder: (final context, final state) {
        setStatusBarColor();
        if (showCompleteView) {
          return Stack(
            children: [
              CreateTagScreen(
                selectedCustomer: selectedCustomer,
                selectedImageList: isSingle ? selectedImageList : [],
                imageBytes: !isSingle ? mergedImage : null,
                tagList: tagList,
                onBack: () => setState(() => showCompleteView = false),
              ),
              if (state.status == AddTagsImageStatus.loading)
                const LoadingWidget(),
            ],
          );
        }
        if (selectedImageList.isEmpty) {
          return _buildBody(context, state);
        }
        setStatusBarColor(useWhiteForeground: true);
        return SafeArea(
          bottom: false,
          child: NestedScrollView(
            floatHeaderSlivers: true,
            headerSliverBuilder: (final context, final value) {
              return [
                SliverAppBar(
                  backgroundColor: Colors.black,
                  automaticallyImplyLeading: false,
                  expandedHeight: isSingle ? 220 : 260,
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: EdgeInsets.zero,
                    background: Container(
                      color: Colors.black,
                      child: Column(
                        children: [
                          Stack(
                            children: [
                              Container(
                                padding: const EdgeInsets.only(top: 8),
                                width: double.maxFinite,
                                child: Center(
                                  child: AnimatedToggle(
                                    initialIndex: isSingle ? 0 : 1,
                                    labels: [
                                      context.l10n.singleImage,
                                      context.l10n.mergedImage,
                                    ],
                                    onToggle: (final index) {
                                      isSingle = index == 0;
                                      if (!isSingle &&
                                          selectedImageList.length > 1) {
                                        final temp = selectedImageList.sublist(
                                          0,
                                          2,
                                        );
                                        selectedDates.clear();
                                        selectedImageList.clear();
                                        selectedImageList.addAll(temp);
                                      }
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  context.router.popForced();
                                },
                                icon: Icon(
                                  Icons.arrow_back_ios,
                                  color: Theme.of(context).colorScheme.surface,
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          if (isSingle)
                            _buildSelectedList()
                          else
                            _buildMergeOptionView(),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                ),
              ];
            },
            body: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  color: Theme.of(context).colorScheme.surface,
                ),
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      color: const Color(0xffF3F3F3),
                      child: Column(
                        children: [
                          const SizedBox(height: 8),
                          Align(
                            alignment: Alignment.topCenter,
                            child: Container(
                              width: 35,
                              height: 5,
                              decoration: ShapeDecoration(
                                color: const Color(0xFFC7C7C7),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(100),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildRoomList(context),
                          const SizedBox(height: 10),
                          const Divider(),
                        ],
                      ),
                    ),
                    _buildImageList(state),
                    if (selectedImageList.isNotEmpty) _buildFooter(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Row _buildMergeOptionView() {
    return Row(
      children: <Widget>[
        _buildHorizontalMerge(),
        const SizedBox(width: 8),
        _buildVerticalMerge(),
      ],
    );
  }

  Expanded _buildVerticalMerge() {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(1),
            width: double.infinity,
            height: 139,
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              color: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: !isHorizontalMerge
                      ? Theme.of(context).primaryColor
                      : Colors.white,
                ),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (selectedImageList.isNotEmpty)
                  Expanded(
                    child: SizedBox(
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(8),
                        ),
                        child: EzCachedNetworkImage(
                          imageUrl:
                              selectedImageList.lastOrNull?.imageUrl ?? '',
                        ),
                      ),
                    ),
                  ),
                const Divider(color: Colors.white),
                Expanded(
                  child: ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(8),
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      child: (selectedImageList.length > 1)
                          ? EzCachedNetworkImage(
                              imageUrl:
                                  selectedImageList.firstOrNull?.imageUrl ?? '',
                            )
                          : _buildPlaceHolderMergeImage(isHorizontal: false),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 6),
          _buildMergeOptionButton(
            isSelected: !isHorizontalMerge,
            label: context.l10n.sampleB,
            iconPath: AppIcons.icVerticalSwap,
          ),
        ],
      ),
    );
  }

  Widget _buildMergeOptionButton({
    required final bool isSelected,
    required final String label,
    required final String iconPath,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () => setState(
            () => isSelected
                ? isHorizontalMerge = isHorizontalMerge
                : isHorizontalMerge = !isHorizontalMerge,
          ),
          child: Container(
            padding: const EdgeInsets.fromLTRB(12, 5, 4, 5),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 0.50,
                  color: isSelected ? Colors.white : const Color(0xFF313131),
                ),
                borderRadius: BorderRadius.circular(100),
              ),
              color: isSelected ? Colors.white : Colors.transparent,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.surface,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 22,
                  height: 22,
                  decoration: ShapeDecoration(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Colors.transparent,

                    shape: OvalBorder(
                      side: isSelected
                          ? BorderSide.none
                          : const BorderSide(width: 1.20, color: Colors.white),
                    ),
                  ),
                  child: isSelected
                      ? const Center(
                          child: Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 4),
        InkWell(
          onTap: () => setState(() {
            final temp = selectedImageList.reversed.toList();
            selectedImageList.clear();
            selectedImageList.addAll(temp);
          }),
          child: EZResources.image(
            ImageParams(name: iconPath, size: const ImageSize.square(32)),
          ),
        ),
      ],
    );
  }

  Expanded _buildHorizontalMerge() {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(1),
            height: 139,
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              color: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: isHorizontalMerge
                      ? Theme.of(context).primaryColor
                      : Colors.white,
                ),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (selectedImageList.isNotEmpty)
                  Expanded(
                    child: SizedBox(
                      height: double.infinity,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.horizontal(
                          left: Radius.circular(8),
                        ),
                        child: EzCachedNetworkImage(
                          imageUrl:
                              selectedImageList.lastOrNull?.imageUrl ?? '',
                        ),
                      ),
                    ),
                  ),
                const VerticalDivider(color: Colors.white, width: 1),
                Expanded(
                  child: ClipRRect(
                    borderRadius: const BorderRadius.horizontal(
                      right: Radius.circular(8),
                    ),
                    child: SizedBox(
                      height: double.infinity,
                      child: (selectedImageList.length > 1)
                          ? EzCachedNetworkImage(
                              imageUrl:
                                  selectedImageList.firstOrNull?.imageUrl ?? '',
                            )
                          : _buildPlaceHolderMergeImage(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 6),
          _buildMergeOptionButton(
            isSelected: isHorizontalMerge,
            label: context.l10n.sampleA,
            iconPath: AppIcons.icHorizontalSwap,
          ),
        ],
      ),
    );
  }

  ColoredBox _buildPlaceHolderMergeImage({final bool isHorizontal = true}) {
    if (isHorizontal) {
      return ColoredBox(
        color: Colors.white,
        child: GridView(
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 25,
            crossAxisSpacing: 25,
          ),
          children: List.generate(
            20,
            (final index) => Container(
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xffF3F3F3),
              ),
            ),
          ),
        ),
      );
    }
    return ColoredBox(
      color: Colors.white,
      child: GridView(
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          mainAxisSpacing: 18,
          crossAxisSpacing: 24,
        ),
        children: List.generate(
          24,
          (final index) => Container(
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0xffF3F3F3),
            ),
          ),
        ),
      ),
    );
  }

  SizedBox _buildSelectedList() {
    return SizedBox(
      height: 145,
      width: double.maxFinite,
      child: ListView.separated(
        separatorBuilder: (final context, final index) =>
            const SizedBox(width: 6),
        itemBuilder: (final context, final index) => ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: EzCachedNetworkImage(
            height: 140,
            width: 140,
            imageUrl: selectedImageList[index]?.imageUrl,
          ),
        ),
        scrollDirection: Axis.horizontal,
        itemCount: selectedImageList.length,
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        const Divider(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Left: selected images
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.l10n.selectedImage(
                        selectedImageList.length.toString(),
                      ),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).hintColor,
                      ),
                    ),
                    const SizedBox(height: 6),
                    SizedBox(
                      height: 50,
                      child: ListView.separated(
                        itemBuilder: (final context, final index) => InkWell(
                          onTap: () {
                            setState(() {
                              selectedImageList.removeAt(index);
                            });
                          },
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: EzCachedNetworkImage(
                                  imageUrl: selectedImageList[index]?.imageUrl,
                                  width: 45,
                                  height: 45,
                                ),
                              ),
                              Positioned(
                                child: Container(
                                  width: 45,
                                  height: 45,
                                  decoration: BoxDecoration(
                                    color: Colors.black38,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        separatorBuilder: (final context, final index) =>
                            const SizedBox(width: 6),
                        itemCount: selectedImageList.length,
                        scrollDirection: Axis.horizontal,
                      ),
                    ),
                  ],
                ),
              ),

              // Right: "Tiếp tục" button
              SizedBox(
                height: 48,
                width: 120,
                child: ElevatedButton(
                  onPressed: () async {
                    try {
                      if (isSingle) {
                        showCompleteView = true;
                        setState(() {});
                        return;
                      } else {
                        if (selectedImageList.length < 2) {
                          return;
                        }
                        mergedImage = null;
                        await showGeneralDialog(
                          context: context,
                          pageBuilder: (_, final __, final ___) {
                            return _buildPreviewMergeImage();
                          },
                        ).then((final result) async {
                          if (result == true) {
                            if (mounted) {
                              LoadingDialog.instance.show(context);
                            }
                            mergedImage = isHorizontalMerge
                                ? await horizontalController.capture()
                                : await verticalController.capture();
                            if (mounted) {
                              LoadingDialog.instance.hide(context);
                            }
                            if (mergedImage == null) {
                              if (mounted) {
                                Alert.showAlert(
                                  AlertParams(
                                    context,
                                    context.l10n.mergedImageFailed,
                                  ),
                                );
                              }
                              return;
                            }
                            showCompleteView = true;
                            setState(() {});
                          }
                        });
                      }
                    } catch (_) {
                      if (mounted) {
                        LoadingDialog.instance.hide(context);
                        Alert.showAlert(
                          AlertParams(context, context.l10n.unknown),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isSingle || selectedImageList.length == 2
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).disabledColor,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        context.l10n.continuous,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.surface,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward,
                        color: Theme.of(context).colorScheme.surface,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Scaffold _buildPreviewMergeImage() {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Text(
              context.l10n.done,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Colors.white,
                fontSize: 15,
              ),
            ),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
          ),
        ],
      ),
      backgroundColor: Colors.black,
      body: isHorizontalMerge
          ? WidgetsToImage(
              controller: horizontalController,
              child: Center(
                child: AspectRatio(
                  aspectRatio: 4 / 3,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: .5),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: double.infinity,
                            child: InteractiveViewer(
                              maxScale: 4.0,
                              child: EzCachedNetworkImage(
                                imageUrl:
                                    selectedImageList.lastOrNull?.imageUrl ??
                                    '',
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                        Divider(color: Colors.grey.withValues(alpha: .5)),
                        Expanded(
                          child: SizedBox(
                            height: double.infinity,
                            child: InteractiveViewer(
                              maxScale: 4.0,
                              child: EzCachedNetworkImage(
                                imageUrl:
                                    selectedImageList.firstOrNull?.imageUrl ??
                                    '',
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
          : WidgetsToImage(
              controller: verticalController,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: .5)),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: SizedBox(
                        width: double.infinity,
                        child: InteractiveViewer(
                          maxScale: 4.0,
                          child: EzCachedNetworkImage(
                            imageUrl:
                                selectedImageList.lastOrNull?.imageUrl ?? '',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    Divider(color: Colors.grey.withValues(alpha: .5)),
                    Expanded(
                      child: SizedBox(
                        width: double.infinity,
                        child: InteractiveViewer(
                          maxScale: 4.0,
                          child: EzCachedNetworkImage(
                            imageUrl:
                                selectedImageList.firstOrNull?.imageUrl ?? '',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  GestureDetector _buildBody(
    final BuildContext context,
    final AddTagsImageState state,
  ) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: FocusScope.of(context).unfocus,
      child: BaseLayout(
        title: Text(
          context.l10n.addTags,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: 18),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSearchBar(context),
              const SizedBox(height: 10),
              _buildRoomList(context),
              const SizedBox(height: 10),
              _buildImageList(state),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _buildImageList(final AddTagsImageState state) {
    return Expanded(
      child: Stack(
        children: [
          ListView.separated(
            padding: EdgeInsets.zero,
            itemCount: imageList.length,
            separatorBuilder: (_, final __) => const SizedBox(height: 16),
            itemBuilder: (final context, final index) {
              return Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            imageList[index]?.createdDate ?? '',
                            style: Theme.of(context).textTheme.labelLarge,
                          ),
                        ),
                        SizedBox(
                          width: 112,
                          child: RRectCheckboxTitle(
                            isCheck: selectedDates.contains(
                              imageList[index]?.createdDate,
                            ),
                            isRevert: true,
                            padding: EdgeInsets.zero,
                            borderRadius: 100,
                            unCheckBorderColor: Theme.of(context).disabledColor,
                            size: 22,
                            titleWidget: Text(
                              context.l10n.selectionAll,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    fontSize: 13,
                                    color: Theme.of(context).hintColor,
                                  ),
                            ),
                            onChanged: ({required final bool value}) {
                              imageList[index]?.images.forEach(
                                (final image) =>
                                    selectedImageList.remove(image),
                              );
                              if (value) {
                                selectedDates.add(
                                  imageList[index]?.createdDate,
                                );
                                selectedImageList.addAll(
                                  imageList[index]?.images
                                          .map((final image) => image)
                                          .toList() ??
                                      [],
                                );
                              } else {
                                selectedDates.remove(
                                  imageList[index]?.createdDate,
                                );
                              }
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    GridView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 3,
                            mainAxisSpacing: 3,
                          ),
                      itemBuilder: (BuildContext _, final int i) {
                        final image = imageList[index]?.images[i];
                        return InkWell(
                          onTap: () {
                            _onSelectedImage(image, index);
                          },
                          child: Stack(
                            children: [
                              Positioned.fill(
                                child: EzCachedNetworkImage(
                                  imageUrl: image?.imageUrl,
                                ),
                              ),
                              if (image?.isTagged ?? false)
                                Positioned(
                                  left: 4,
                                  top: 4,
                                  child: EZResources.image(
                                    ImageParams(
                                      name: AppIcons.icTag,
                                      size: const ImageSize.square(24),
                                    ),
                                  ),
                                ),
                              if (selectedImageList.contains(image))
                                Positioned(
                                  right: 4,
                                  top: 4,
                                  child: Container(
                                    height: 20,
                                    width: 20,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.surface,
                                      ),
                                    ),
                                    child: Center(
                                      child: EZResources.image(
                                        ImageParams(
                                          name: AppIcons.icCheck,
                                          size: const ImageSize(9, 7),
                                          color: Theme.of(
                                            context,
                                          ).colorScheme.surface,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                      itemCount: imageList[index]?.images.length,
                    ),
                  ],
                ),
              );
            },
          ),
          if (state.status == AddTagsImageStatus.loading) const LoadingWidget(),
        ],
      ),
    );
  }

  void _onSelectedImage(
    final AddTagsImageGetImageListItemsImages? image,
    final int index,
  ) {
    if (selectedImageList.contains(image)) {
      selectedImageList.remove(image);
      //check if group image is not full selected
      //then disable check all
      final unCheckAll =
          imageList[index]?.images.any(
            (final image) => !selectedImageList.contains(image),
          ) ??
          false;
      if (unCheckAll) {
        selectedDates.remove(imageList[index]?.createdDate);
      }
    } else {
      if (!isSingle && selectedImageList.length > 1) {
        // if selectedImageList == 2 and merge image
        // then replace first image
        selectedImageList.removeAt(0);
        selectedImageList.insert(0, image);
      } else {
        selectedImageList.insert(0, image);
      }

      //check if group image is full selected
      //then active check all
      final checkAll =
          !(imageList[index]?.images.any(
                (final image) => !selectedImageList.contains(image),
              ) ??
              false);
      if (checkAll && !selectedDates.contains(imageList[index]?.createdDate)) {
        selectedDates.add(imageList[index]?.createdDate);
      }
    }
    setState(() {});
  }

  SingleChildScrollView _buildRoomList(final BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Wrap(
        spacing: 4,
        children: roomList.map((final room) {
          final isSelected = selectedRoomIds.contains(room?.roomCode ?? '');
          return InkWell(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100),
                color: Theme.of(context).colorScheme.surface,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : const Color(0xffC7C7C7),
                ),
              ),
              child: Text(
                room?.roomName ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : const Color(0xff828282),
                ),
              ),
            ),
            onTap: () {
              if (isSelected) {
                selectedRoomIds.remove(room?.roomCode ?? '');
              } else {
                if (room?.roomCode?.isEmpty ?? true) {
                  selectedRoomIds.clear();
                } else {
                  selectedRoomIds.removeWhere((final e) => e.isEmpty);
                }
                selectedRoomIds.clear();
                selectedRoomIds.add(room?.roomCode ?? '');
                context.read<AddTagsImageBloc>().add(
                  AddTagsImageGetImageListed(
                    AddTagsImageGetImageListRequestParams(
                      selectedCustomer?.customerCode,
                      itemGroupId: selectedRoomIds.firstOrNull,
                    ),
                  ),
                );
              }
              setState(() {});
            },
          );
        }).toList(),
      ),
    );
  }

  Column _buildSearchBar(final BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.searchCustomerToAddTag,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Theme.of(context).hintColor),
        ),
        const SizedBox(height: 6),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => onTapSearch(),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(width: 0.35, color: const Color(0xffBCB9C0)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    selectedCustomer != null
                        ? '${selectedCustomer?.customerCode} - '
                              '${selectedCustomer?.customerName}'
                        : context.l10n.searchCustomer,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: selectedCustomer != null
                          ? Theme.of(context).textTheme.bodyLarge?.color
                          : const Color(0xff7A7582),
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Theme.of(context).hintColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> onTapSearch() async {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(14)),
      ),
      builder: (final _) => ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
        child: SizedBox(
          height: MediaQuery.sizeOf(context).height * 0.9,
          child: StatefulBuilder(
            builder: (final _, final modalState) {
              return BlocProvider.value(
                value: context.read<AddTagsImageBloc>(),
                child: BlocBuilder<AddTagsImageBloc, AddTagsImageState>(
                  builder: (final context, final state) {
                    return Stack(
                      children: [
                        SearchCustomerView(
                          searchController: searchController,
                          onChanged: (final keySearch) {
                            timer?.cancel();
                            timer = Timer(
                              const Duration(milliseconds: 700),
                              () async {
                                context.read<AddTagsImageBloc>().add(
                                  AddTagsImageUserSearched(
                                    CustomerListRequestParams(
                                      search: keySearch,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          onClear: () {
                            searchController.clear();
                            customerList.clear();
                            modalState(() {});
                          },
                          onSelected: (final customer) {
                            selectedCustomer = customer;
                            context.read<AddTagsImageBloc>().add(
                              AddTagsImageGetImageListed(
                                AddTagsImageGetImageListRequestParams(
                                  selectedCustomer?.customerCode,
                                  itemGroupId: selectedRoomIds.firstOrNull,
                                ),
                              ),
                            );
                            Navigator.of(context).pop();
                          },
                          customerList: customerList,
                        ),

                        if (state.status == AddTagsImageStatus.loading)
                          const LoadingWidget(),
                      ],
                    );
                  },
                ),
              );
            },
          ),
        ),
      ),
    ).whenComplete(() {
      searchController.clear();
      customerList.clear();
    });
  }
}
